import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsArray, ValidateNested, IsNumber, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';

// DTO for content items (similar to broker)
export class ContentItemDto {
  @ApiProperty({ description: 'Title of the content item' })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiProperty({ description: 'Content array', type: [String] })
  @IsArray()
  @IsOptional()
  content?: string[];
}

// DTO for cashback items
export class CashbackDto {
  @ApiProperty({ description: 'Title of the cashback item' })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiProperty({ description: 'Content array', type: [String] })
  @IsArray()
  @IsOptional()
  content?: string[];

  @ApiProperty({ description: 'CTA link' })
  @IsString()
  @IsOptional()
  ctaLink?: string;

  @ApiProperty({ description: 'CTA title' })
  @IsString()
  @IsOptional()
  ctaTitle?: string;
}

// DTO for FAQ items
export class FAQDto {
  @ApiProperty({ description: 'FAQ question' })
  @IsString()
  @IsOptional()
  question?: string;

  @ApiProperty({ description: 'FAQ answer' })
  @IsString()
  @IsOptional()
  answer?: string;
}

// DTO for user reviews
export class UserReviewDto {
  @ApiProperty({ description: 'User name' })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({ description: 'Rating between 1 and 5', minimum: 1, maximum: 5 })
  @IsNumber()
  @IsOptional()
  @Min(1)
  @Max(5)
  rating?: number;

  @ApiProperty({ description: 'Review title' })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiProperty({ description: 'Review date' })
  @IsString()
  @IsOptional()
  date?: string;

  @ApiProperty({ description: 'Review description' })
  @IsString()
  @IsOptional()
  description?: string;
}

// DTO for social links object
export class SocialLinksDto {
  @ApiProperty({ description: 'Twitter profile link' })
  @IsString()
  @IsOptional()
  twitterLink?: string;

  @ApiProperty({ description: 'Facebook profile link' })
  @IsString()
  @IsOptional()
  facebook?: string;

  @ApiProperty({ description: 'Instagram profile link' })
  @IsString()
  @IsOptional()
  instagram?: string;

  @ApiProperty({ description: 'LinkedIn profile link' })
  @IsString()
  @IsOptional()
  linkedInLink?: string;
}

// DTO for review content items
export class ReviewContentDto {
  @ApiProperty({ description: 'Title of the content item' })
  @IsString()
  title: string;

  @ApiProperty({ description: 'Content text' })
  @IsString()
  content: string;
}

// DTO for review items
export class ReviewItemDto {
  @ApiProperty({ description: 'Main title for the review section' })
  @IsString()
  mainTitle: string;

  @ApiProperty({
    description: 'Array of content items with title and content',
    type: [ReviewContentDto]
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ReviewContentDto)
  content: ReviewContentDto[];
}

// DTO for rating exchange
export class RateExchangeDto {
  @ApiProperty({ description: 'Rating value between 1 and 5', minimum: 1, maximum: 5 })
  @IsNumber()
  @Min(1)
  @Max(5)
  rating: number;

  @ApiProperty({ description: 'Review text', required: false })
  @IsOptional()
  @IsString()
  review?: string;
}

// Create Exchange DTO
export class CreateExchangeDto {
  @ApiProperty({ description: 'Exchange title' })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiProperty({ description: 'Exchange image URL' })
  @IsString()
  @IsOptional()
  image?: string;

  @ApiProperty({ description: 'Exchange details' })
  @IsString()
  @IsOptional()
  details?: string;

  @ApiProperty({
    description: 'General information array',
    type: [ContentItemDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ContentItemDto)
  generalInfo?: ContentItemDto[];

  @ApiProperty({ description: 'Account options array', type: [ContentItemDto] })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ContentItemDto)
  AccountOptions?: ContentItemDto[];

  @ApiProperty({
    description: 'Fee and funding array',
    type: [ContentItemDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ContentItemDto)
  feeAndFunding?: ContentItemDto[];

  @ApiProperty({
    description: 'Trading information array',
    type: [ContentItemDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ContentItemDto)
  trading?: ContentItemDto[];

  @ApiProperty({
    description: 'Platform information array',
    type: [ContentItemDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ContentItemDto)
  platform?: ContentItemDto[];

  @ApiProperty({
    description: 'Customer service array',
    type: [ContentItemDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ContentItemDto)
  customerService?: ContentItemDto[];

  @ApiProperty({
    description: 'New cashback array',
    type: [CashbackDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CashbackDto)
  newCashback?: CashbackDto[];

  @ApiProperty({
    description: 'Existing cashback array',
    type: [CashbackDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CashbackDto)
  existingCashback?: CashbackDto[];

  @ApiProperty({ description: 'Cashback data' })
  @IsOptional()
  @IsString()
  cashbackData?: string;

  @ApiProperty({
    description: 'Cashback FAQ array',
    type: [FAQDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FAQDto)
  cashbackFAQ?: FAQDto[];

  @ApiProperty({
    description: 'Exchange FAQ array',
    type: [FAQDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FAQDto)
  exchangeFAQ?: FAQDto[];

  @ApiProperty({
    description: 'User reviews array',
    type: [UserReviewDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UserReviewDto)
  Reviews?: UserReviewDto[];

  @ApiProperty({
    description: 'Social links object',
    type: SocialLinksDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => SocialLinksDto)
  socialLinks?: SocialLinksDto;

  @ApiProperty({
    description: 'Reviews array with mainTitle and content objects',
    type: [ReviewItemDto],
    required: false
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ReviewItemDto)
  reviews?: ReviewItemDto[];

  @ApiProperty({ description: 'Exchange status', default: 'draft' })
  @IsOptional()
  @IsString()
  status?: string;
}

// Update Exchange DTO
export class UpdateExchangeDto {
  @ApiProperty({ description: 'Exchange title' })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiProperty({ description: 'Exchange image URL' })
  @IsOptional()
  @IsString()
  image?: string;

  @ApiProperty({ description: 'Exchange details' })
  @IsOptional()
  @IsString()
  details?: string;

  @ApiProperty({
    description: 'General information array',
    type: [ContentItemDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ContentItemDto)
  generalInfo?: ContentItemDto[];

  @ApiProperty({ description: 'Account options array', type: [ContentItemDto] })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ContentItemDto)
  AccountOptions?: ContentItemDto[];

  @ApiProperty({
    description: 'Fee and funding array',
    type: [ContentItemDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ContentItemDto)
  feeAndFunding?: ContentItemDto[];

  @ApiProperty({
    description: 'Trading information array',
    type: [ContentItemDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ContentItemDto)
  trading?: ContentItemDto[];

  @ApiProperty({
    description: 'Platform information array',
    type: [ContentItemDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ContentItemDto)
  platform?: ContentItemDto[];

  @ApiProperty({
    description: 'Customer service array',
    type: [ContentItemDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ContentItemDto)
  customerService?: ContentItemDto[];

  @ApiProperty({
    description: 'New cashback array',
    type: [CashbackDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CashbackDto)
  newCashback?: CashbackDto[];

  @ApiProperty({
    description: 'Existing cashback array',
    type: [CashbackDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CashbackDto)
  existingCashback?: CashbackDto[];

  @ApiProperty({ description: 'Cashback data' })
  @IsOptional()
  @IsString()
  cashbackData?: string;

  @ApiProperty({
    description: 'Cashback FAQ array',
    type: [FAQDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FAQDto)
  cashbackFAQ?: FAQDto[];

  @ApiProperty({
    description: 'Exchange FAQ array',
    type: [FAQDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FAQDto)
  exchangeFAQ?: FAQDto[];

  @ApiProperty({
    description: 'User reviews array',
    type: [UserReviewDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UserReviewDto)
  Reviews?: UserReviewDto[];

  @ApiProperty({
    description: 'Social links object',
    type: SocialLinksDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => SocialLinksDto)
  socialLinks?: SocialLinksDto;

  @ApiProperty({
    description: 'Reviews array with mainTitle and content objects',
    type: [ReviewItemDto],
    required: false
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ReviewItemDto)
  reviews?: ReviewItemDto[];

  @ApiProperty({ description: 'Exchange status' })
  @IsOptional()
  @IsString()
  status?: string;
}
